import java.util.*;

/**
 小明，小王，小李三人正在进行一个游戏。游戏有
 n个回合，每个人都有一个字符串，三人的字符串长度相等。每个回合他们必须更改自己字符串中的一个字母。
 最后每个人的分数是自己的字符串中出现次数最多的字母的数量。分数最高者获胜，输出获胜者名字，小明获胜输出xiaoming，小王获胜输出xiaowang，小李获胜输出xiaoli，
 如果有两个或者两个以上相同的最高分输出draw。
 输入例子：
 7
 treasurehunt
 threefriends
 hiCodeforces
 输出例子：
 xiaowang

 */

public class test {
    public static void main(String[] args) {
        Scanner sc=new Scanner(System.in);
        int n=sc.nextInt();//回合数
        sc.nextLine();//读取换行符
        String xiaoming=sc.nextLine();
        String xiaowang=sc.nextLine();
        String xiaoli=sc.nextLine();
        int xiaomingScore=0;
        int xiaowangScore=0;
        int xiaoliScore=0;
        for (int i = 0; i < n; i++) {
            xiaomingScore+=getMaxCount(xiaoming);
            xiaowangScore+=getMaxCount(xiaowang);
            xiaoliScore+=getMaxCount(xiaoli);
        }
        if(xiaomingScore>xiaowangScore&&xiaomingScore>xiaoliScore){
            System.out.println("xiaoming");
        }else if(xiaowangScore>xiaomingScore&&xiaowangScore>xiaoliScore){
            System.out.println("xiaowang");
        }else if(xiaoliScore>xiaomingScore&&xiaoliScore>xiaowangScore){
            System.out.println("xiaoli");
        }else{
            System.out.println("draw");
        }


    }

    static int getMaxCount(String str) {
        int max=0;
        for (int i = 0; i < str.length(); i++) {
            int count=0;
            for (int j = 0; j < str.length(); j++) {
                if(str.charAt(i)==str.charAt(j)){
                    count++;
                }
            }
            if(count>max){
                max=count;
            }
        }
        return max;
    }
}

