import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        //- 获取所有枚举成员并遍历输出所有成员的星期和课程值
        for (Week week : Week.values()) {
            System.out.println(week.name()+ " " + week.getCourse());
        }
        //- 控制台输入编号,打印出当前编号对应的星期和课程
        Scanner sc = new Scanner(System.in);
        System.out.println("请输入编号:");
        int week = sc.nextInt();
        for (Week week1 : Week.values()) {
            if (week1.getWeek() == week) {
                System.out.println(week1.getChinese() + " " + week1.getCourse());
            }
        }
        //- 控制台输入课程,打印出对应的编号和星期
        System.out.println("请输入课程:");
        String course = sc.next();
        for (Week week1 : Week.values()) {
            if (week1.getCourse().equals(course)) {
                System.out.println(week1.getWeek() + " " + week1.getChinese());
            }
        }

    }
}