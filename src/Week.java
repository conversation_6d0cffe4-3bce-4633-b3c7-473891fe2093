
public enum Week {
    MONDAY(1,"星期一","数学"),
    TUESDAY(2,"星期二","语文"),
    WEDNESDAY(3,"星期三","英语"),
    THURSDAY(4,"星期四","物理"),
    FRIDAY(5,"星期五","化学");
    private int week;
    private String chinese;
    private String course;
    Week(int week,String chinese,String course){
        this.week = week;
        this.chinese = chinese;
        this.course = course;
    }
    public int getWeek(){
        return week;
    }
    public String getChinese(){
        return chinese;
    }
    public String getCourse(){
        return course;
    }
}
