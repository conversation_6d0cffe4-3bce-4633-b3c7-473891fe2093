

//模拟银行业务办理
//1.有5个窗口同时办理存款服务
//2.每次存入100元，一共存10次
//3.使用synchronized完成同步

public class Bank {
    private Integer num=10;
    final Object obj=new Object();

    public void save(){
        while(true){
            try {
                Thread.sleep(200);//1抢到了
                //synchronized代码块中,同一时刻只能有一个线程执行
                //obj可以是任意对象,只要保证是唯一的
                //定义存款
                int money=0;
                synchronized (obj){
                    if(num>0){//
                        //1  2 3
                        System.out.println(Thread.currentThread().getName()+"存入100元，累计金额:"+(money+=100));
                        num--;
                    }else{
                        break;
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static void main(String[] args) {
        Bank bank =new Bank();
        new Thread(()->{
            bank.save();
        },"A窗口").start();

        new Thread(()->{
            bank.save();
        },"B窗口").start();

        new Thread(()->{
            bank.save();
        },"C窗口").start();
        new Thread(()->{
            bank.save();
        },"D窗口").start();
        new Thread(()->{
            bank.save();
        },"E窗口").start();
    }


}
