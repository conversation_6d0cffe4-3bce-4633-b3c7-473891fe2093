public class SaleTicket {

    private Integer num=100;
    final Object obj=new Object();

    public void sale(){
        while(true){
            try {
                Thread.sleep(200);//1抢到了
                //synchronized代码块中,同一时刻只能有一个线程执行
                //obj可以是任意对象,只要保证是唯一的
                synchronized (obj){
                    if(num>0){//
                        //1  2 3
                        System.out.println(Thread.currentThread().getName()+"卖出了1张票,剩余"+(--num)+"张");
                    }else{
                        break;
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static void main(String[] args) {

        SaleTicket saleTicket=new SaleTicket();
        new Thread(()->{
            saleTicket.sale();
        },"A窗口").start();

        new Thread(()->{
            saleTicket.sale();
        },"B窗口").start();

        new Thread(()->{
            saleTicket.sale();
        },"C窗口").start();

    }


}
