public class pd1 {
    Integer num;
    boolean flag=false;
    public synchronized void provider(int num){//每次生成一个num值（生产）
        if(flag){
            try {
                wait();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        System.out.println("生产者正在生产数据...");
        this.num=num;
        flag=true;
        notify();//唤醒消费者

    }

    public synchronized void consumer(){//打印出num的值（消费）
        if(!flag){
            try {
                wait();//consumer先执行，但是wait后会进入等待状态，并且释放锁（让生产者执行,执行完毕后唤醒消费者）
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        System.out.println("消费者正在消费:"+num);
        flag=true;
        notify();//唤醒生产者

    }
    public static void main(String[] args) {
        Product product=new Product();
        new Thread(()->{
            for (int i = 1; i <=10 ; i++) {
                product.provider(i);
            }
        }).start();

        new Thread(()->{
            for (int i = 1; i <=10 ; i++) {
                product.consumer();
            }
        }).start();



    }
}
